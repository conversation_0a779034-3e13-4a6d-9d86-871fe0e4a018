'use client';
import React, { useState } from 'react';
import Loader from '../Loader'; // Assuming Loader is in the same directory
import { BiPlus } from 'react-icons/bi'; // For the plus icon
import { FiCheck } from 'react-icons/fi'; // For the checkmark icon (not used in current logic, but kept for future)
import { RxCross1 } from 'react-icons/rx'; // For the X icon (cross for pumped state)

// Inline SVG for the lips icon
const LipIcon = ({ colorClass = 'text-current' }) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    viewBox="0 0 24 24"
    fill="currentColor"
    className={`h-10 w-10 transition-colors duration-300 ${colorClass}`}
  >
    <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zM12 20c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8zm-1-7.5c-.83 0-1.5-.67-1.5-1.5S10.17 10 11 10s1.5.67 1.5 1.5S11.83 12.5 11 12.5zm4 0c-.83 0-1.5-.67-1.5-1.5S14.17 10 15 10s1.5.67 1.5 1.5S15.83 12.5 15 12.5zm-3.5 4.5c-2.33 0-4.32-1.42-5.18-3.41L12 11.5l5.18 3.09c-.86 1.99-2.85 3.41-5.18 3.41z"/>
  </svg>
);


const CircleIconButton = ({
  onClick,
  disabled = false,
  loader = false,
  icon: IconComponent, // The icon component to render (e.g., LipIcon, BiPlus)
  isHickied = false, // Specific state for Hickie button (red lips)
  isPumped = false, // Specific state for Pump button (X icon, spin)
}) => {
  const [isLoading, setIsLoading] = useState(loader);

  console.log(`CircleIconButton: Received isHickied=${isHickied}, isPumped=${isPumped}. IconComponent: ${IconComponent ? IconComponent.name : 'None'}`);

  return (
    <button
      onClick={(e) => {
        if (loader !== undefined) {
          setIsLoading(true);
        }
        onClick(e);
      }}
      disabled={disabled || isLoading}
      // Fixed circular sizing and styling for these specific buttons
      className={`
        w-20 h-20 rounded-full flex items-center justify-center flex-shrink-0
        bg-white hover:bg-gray-100 text-black border-2 border-gray-300 shadow-md
        transition-all duration-300 ease-in-out
        relative overflow-hidden
      `}
    >
      {isLoading ? (
        <Loader />
      ) : (
        <span
          className={`
            ${isHickied && IconComponent === LipIcon ? 'text-red-500' : 'text-current'}
            ${isPumped ? 'rotate-90' : 'rotate-0'} /* Spin always applies if pumped, regardless of icon */
            transition-transform duration-300 ease-in-out
          `}
        >
          {/* Conditional rendering for icons specific to these circular buttons */}
          {/* If it's the LipIcon, apply red color based on isHickied.
              If it's BiPlus and isPumped, show RxCross1 (X), otherwise BiPlus (+). */}
          {IconComponent === LipIcon ? (
            <LipIcon colorClass={isHickied ? 'text-red-500' : 'text-current'} />
          ) : (isPumped ? <RxCross1 className="h-10 w-10" /> : <IconComponent className="h-10 w-10" />)}
        </span>
      )}
    </button>
  );
};

export default CircleIconButton;
