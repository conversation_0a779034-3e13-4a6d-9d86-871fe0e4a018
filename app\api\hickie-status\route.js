// app/api/hickie-status/route.js

import { NextResponse } from 'next/server';
import { MongoClient } from 'mongodb';
import clientPromise from '@/lib/mongodb'; // Your MongoDB connection helper

// Helper to get database instance for MongoDB
async function getDb() {
  const client = await clientPromise;
  return client.db('Pumpkin'); // Your MongoDB database name
}

// --- GET Request: Check Hickie Status ---
export async function GET(req) {
  const db = await getDb();
  try {
    const { searchParams } = new URL(req.url);
    const likerEmail = searchParams.get('likerEmail');
    const likedUserEmail = searchParams.get('likedUserEmail');

    if (!likerEmail || !likedUserEmail) {
      return NextResponse.json({ success: false, message: 'Missing likerEmail or likedUserEmail query parameters' }, { status: 400 });
    }

    const existingHickie = await db.collection('Hickies').findOne({
      likerEmail: likerEmail,
      likedUserEmail: likedUserEmail
    });

    return NextResponse.json({ success: true, isHickied: !!existingHickie }, { status: 200 });
  } catch (error) {
    console.error('Error checking hickie status:', error);
    return NextResponse.json({ success: false, message: 'Failed to check hickie status', error: error.message }, { status: 500 });
  }
}
