const { NextResponse } = require("next/server");
const { MongoClient } = require("mongodb");
const { default: clientPromise } = require("@/lib/mongodb");

export async function POST(req) {
  const client = await clientPromise;
  const db = client.db("Pumpkin"); // This is your MongoDB database name
  const body = await req.json();

  // NEW: Add default hickies, hickiedBy, receivedPumps, and pumpedBy fields if they are not already present in the request body
  const newUser = {
    ...body,
    hickies: body.hickies !== undefined ? body.hickies : 0, // Initialize to 0 if not provided
    hickiedBy: body.hickiedBy !== undefined ? body.hickiedBy : [], // Initialize to empty array if not provided
    receivedPumps: body.receivedPumps !== undefined ? body.receivedPumps : 0, // Initialize to 0 if not provided
    pumpedBy: body.pumpedBy !== undefined ? body.pumpedBy : [], // Initialize to empty array if not provided
  };

  const candidateData = await db.collection("Users").insertOne(newUser); // Insert the modified user data
  return NextResponse.json(candidateData);
}

export async function GET() {
  try {
    const client = await clientPromise;
    const db = client.db("Pumpkin"); // This is your MongoDB database name
    const userData = await db.collection("Users").find().sort({}).toArray();
    return NextResponse.json(userData);
  } catch (error) {
    console.error(error);
    return NextResponse.json("error", { status: 500 });
  }
}
