// app/api/profile-hickie/route.js
import clientPromise from "@/lib/mongodb";

export async function GET(req) {
  try {
    const { searchParams } = new URL(req.url);
    const targetEmail = searchParams.get("targetEmail");
    if (!targetEmail) {
      return new Response(JSON.stringify({ error: "Missing targetEmail" }), { status: 400 });
    }
    const client = await clientPromise;
    const db = client.db("Pumpkin");
    
    // Try finding the user in "Users" first, then fall back to "users"
    let user = await db.collection("Users").findOne(
      { email: targetEmail },
      { projection: { hickies: 1, hickiedBy: 1, _id: 0 } }
    );

    if (!user) {
      user = await db.collection("users").findOne(
        { email: targetEmail },
        { projection: { hickies: 1, hickiedBy: 1, _id: 0 } }
      );
    }

    if (!user) {
      return new Response(JSON.stringify({ error: "User not found" }), { status: 404 });
    }
    
    return new Response(JSON.stringify({
      hickies: user.hickies || 0,
      hickiedBy: user.hickiedBy || [],
      success: true
    }), { status: 200 });
  } catch (error) {
    console.error("Error fetching hickie:", error);
    return new Response(JSON.stringify({ error: "Internal Server Error" }), { status: 500 });
  }
}

export async function PUT(req) {
  console.log("--- New PUT request to /api/profile-hickie ---");
  try {
    const body = await req.json();
    console.log("Request Body:", body);

    const { targetEmail, hickiedBy, hickies } = body;
    console.log("Extracted Data:", { targetEmail, hickiedBy, hickies });

    if (!targetEmail) {
      console.log("Error: Missing targetEmail");
      return new Response(JSON.stringify({ error: "Missing targetEmail" }), { status: 400 });
    }

    const client = await clientPromise;
    const db = client.db("Pumpkin");
    console.log("Database connected.");

    // First, try to update in the "Users" collection
    let updateResult = await db.collection("Users").updateOne(
      { email: targetEmail },
      { $set: { hickiedBy, hickies } }
    );

    // If no user was matched, try updating in the "users" collection
    if (updateResult.matchedCount === 0) {
      console.log("User not found in 'Users' collection, trying 'users' collection.");
      updateResult = await db.collection("users").updateOne(
        { email: targetEmail },
        { $set: { hickiedBy, hickies } }
      );
    }

    console.log("MongoDB updateOne Result:", updateResult);

    if (updateResult.matchedCount === 0) {
      console.log("Update failed: User not found with email in either collection:", targetEmail);
      return new Response(JSON.stringify({ success: false, error: "User not found" }), { status: 404 });
    }

    if (updateResult.modifiedCount >= 0) { // Can be 0 if data is the same, which is not an error
      console.log("Update successful for email:", targetEmail);
      return new Response(JSON.stringify({ success: true }), { status: 200 });
    } else {
      console.log("Update failed: No modification was made for email:", targetEmail);
      return new Response(JSON.stringify({ success: false, error: "Update failed for an unknown reason." }), { status: 400 });
    }
  } catch (error) {
    console.error("!!! CRITICAL ERROR in /api/profile-hickie PUT:", error);
    return new Response(JSON.stringify({ error: "Internal Server Error" }), { status: 500 });
  }
}
