"use client";

import React, { useEffect, useState } from "react";

// Simple component that fetches and displays the current hickie count
export default function LiveHickieCount({ targetEmail, fallbackCount = 0 }) {
  const [hickieCount, setHickieCount] = useState(fallbackCount);

  useEffect(() => {
    const fetchHickieCount = async () => {
      if (!targetEmail) return;
      
      try {
        const response = await fetch(`/api/profile-hickie?targetEmail=${targetEmail}`);
        const data = await response.json();
        
        if (data.success) {
          setHickieCount(data.hickies || 0);
        }
      } catch (error) {
        console.error("Error fetching hickie count:", error);
        // Keep the fallback count if fetch fails
      }
    };

    fetchHickieCount();
  }, [targetEmail, fallbackCount]);

  return <span>{hickieCount}</span>;
} 