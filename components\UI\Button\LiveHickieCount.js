"use client";

import React, { useEffect, useState } from "react";

// Simple component that fetches and displays the current hickie count
export default function LiveHickieCount({ targetEmail, fallbackCount = 0 }) {
  const [hickieCount, setHickieCount] = useState(fallbackCount);

  useEffect(() => {
    const fetchHickieCount = async () => {
      if (!targetEmail) return;

      try {
        console.log("LiveHickieCount: Fetching hickie count for", targetEmail);
        const response = await fetch(
          `/api/profile-hickie?targetEmail=${targetEmail}`
        );
        const data = await response.json();
        console.log("LiveHickieCount: Received data", data);

        if (data.success) {
          console.log("LiveHickieCount: Setting count to", data.hickies || 0);
          setHickieCount(data.hickies || 0);
        }
      } catch (error) {
        console.error("Error fetching hickie count:", error);
        // Keep the fallback count if fetch fails
      }
    };

    fetchHickieCount();

    // Set up polling to refresh hickie count every 5 seconds
    const interval = setInterval(fetchHickieCount, 5000);

    return () => clearInterval(interval);
  }, [targetEmail, fallbackCount]);

  return <span>{hickieCount}</span>;
}
