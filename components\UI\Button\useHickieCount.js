"use client";

import { useEffect, useState, useCallback } from "react";
import { useCookies } from "next-client-cookies";

// Custom hook for managing hickie state - this will be the universal source of truth
export const useHickieCount = (targetEmail, initialHickies = 0, initialHickiedBy = []) => {
  const cookies = useCookies();
  const currentUserEmail = cookies.get("email");

  const [hickied, setHickied] = useState(false);
  const [hickieCount, setHickieCount] = useState(initialHickies);
  const [hickiedBy, setHickiedBy] = useState(initialHickiedBy);

  // Sync internal state with props when initialHickies/initialHickiedBy or targetEmail changes
  useEffect(() => {
    setHickieCount(initialHickies || 0);
    setHickiedBy(initialHickiedBy || []);
    setHickied((initialHickiedBy || []).includes(currentUserEmail));
  }, [initialHickies, initialHickiedBy, currentUserEmail, targetEmail]);

  const toggleHickie = useCallback(async (onHickieUpdate) => {
    if (!currentUserEmail) {
      alert("Please log in to leave a Hickie.");
      return;
    }

    const isCurrentlyHickied = hickied;

    // Calculate new state values
    let newHickiedBy;
    let newHickieCount;

    if (isCurrentlyHickied) {
      // User is un-hickieing
      newHickiedBy = hickiedBy.filter(email => email !== currentUserEmail);
      newHickieCount = hickieCount - 1;
    } else {
      // User is hickieing
      newHickiedBy = [...hickiedBy, currentUserEmail];
      newHickieCount = hickieCount + 1;
    }

    // Optimistically update local state (UI responds immediately)
    setHickied(!isCurrentlyHickied);
    setHickiedBy(newHickiedBy);
    setHickieCount(newHickieCount);

    try {
      const res = await fetch("/api/profile-hickie", {
        method: "PUT",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          targetEmail,
          hickiedBy: newHickiedBy,
          hickies: newHickieCount,
        }),
      });
    
      const result = await res.json();

      if (!res.ok || !result.success) {
        // If API call failed, revert local state
        console.error("PHB: API request failed. Reverting optimistic UI. Status:");
        setHickied(isCurrentlyHickied);
        setHickiedBy(hickiedBy);
        setHickieCount(hickieCount);
        alert("Failed to update Hickie. Please try again.");
      } else {
        // If API call succeeded, confirm the update to parent
        if (onHickieUpdate) {
          onHickieUpdate(targetEmail, newHickieCount, newHickiedBy);
        }
      }
    } catch (error) {
      // Catch any network errors or errors thrown by 'res.json()' if not handled above
      console.error("PHB: Network or unexpected error during toggleHickie. Reverting UI. Error:", error);
      setHickied(isCurrentlyHickied);
      setHickiedBy(hickiedBy);
      setHickieCount(hickieCount);
      alert("An unexpected error occurred. Please try again.");
    }
  }, [hickied, hickiedBy, hickieCount, currentUserEmail, targetEmail]);

  return {
    hickied,
    hickieCount,
    hickiedBy,
    toggleHickie,
    currentUserEmail
  };
}; 