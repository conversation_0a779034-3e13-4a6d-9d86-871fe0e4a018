'use client';
import React, { useState } from 'react';
import Loader from '../Loader'; // Assuming Loader is in the same directory

const Button = ({
  label,
  onClick,
  variant,
  disabled = false,
  className = '', // Allows external classes to be passed
  loader,
}) => {
  const [isLoading, setIsLoading] = useState(loader);

  const getButtonStyles = () => {
    switch (variant) {
          case 'primary':
      return 'bg-black text-white hover:bg-gray-900';
    case 'secondary':
      return 'bg-orange-500 text-white hover:bg-orange-600';
    case 'tertiary':
      return 'bg-white text-black border border-gray-300 hover:bg-gray-100';
    case 'gradient-black':
      return 'bg-gradient-to-r from-black via-gray-900 to-black text-white';
    case 'gradient-orange':
      return 'bg-gradient-to-r from-orange-500 via-yellow-500 to-red-500 text-white';
    default:
      return 'bg-blue-500 hover:bg-blue-600 text-white';
    }
  };

  return (
    <button
      onClick={(e) => {
        if (loader !== undefined) {
          setIsLoading(true);
        }
        onClick(e);
      }}
      disabled={disabled || isLoading}
      className={`
        min-w-48 lg:min-w-48 w-full h-12 lg:py-3
        font-bold rounded-full text-sm lg:text-base
        transition-all duration-300 ease-in-out
        relative overflow-hidden flex items-center justify-center
        ${getButtonStyles()}
        ${className} /* Apply external classes here */
      `}
    >
      {isLoading ? (
        <Loader />
      ) : (
        <span>{label}</span>
      )}
    </button>
  );
};

export default Button;
