"use client";
import React, { useEffect, useState } from "react";
import NotificationItem from "../UI/Notifications/NotificationItem";
import { useCookies } from "next-client-cookies";

export default function NotificationsHome() {
  const [userNotifications, setUserNotifications] = useState([]);
  const [unseenNotifications, setUnseenNotifications] = useState([]);
  const cookies = useCookies();

  // Fetch all notifications: old and new
  const fetchNotifications = async (email) => {
    try {
      // Old notifications
      const shooterResponse = await fetch("/api/shooter");
      const pumpkinResponse = await fetch("/api/pumpkin");
      const commentResponse = await fetch("/api/comment-notification");

      const shooterData = await shooterResponse.json();
      const pumpkinData = await pumpkinResponse.json();
      const commentData = await commentResponse.json();

      // New notifications
      const newNotifResponse = await fetch(
        `/api/notifications?receiverEmail=${encodeURIComponent(email)}`
      );
      const newNotifData = await newNotifResponse.json();

      // Combine all notifications
      const combinedNotifications = [
        ...shooterData,
        ...pumpkinData,
        ...commentData.filter(
          (notification) => notification.recipientEmail === email
        ),
        ...newNotifData,
      ];

      // Filter for current user and sort by timestamp (descending)
      const selectedNotifications = combinedNotifications
        .filter(
          (post) =>
            post.email === email ||
            post.recipientEmail === email ||
            post.receiverEmail === email // for new notifications
        )
        .sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));

      setUserNotifications(selectedNotifications);
    } catch (error) {
      console.error("Error fetching notifications:", error);
    }
  };

  useEffect(() => {
    const email = cookies.get("email");
    if (email) fetchNotifications(email);
  }, [cookies]);

  useEffect(() => {
    // Filter unseen notifications
    const unseen = userNotifications.filter((post) => !post.seen);
    setUnseenNotifications(unseen);

    // (Optional) Mark all as seen when visiting the page (for old notifications)
    unseen.forEach(async (notification) => {
      if (
        notification._id &&
        (notification.type === "comment" || notification.type === "shooter")
      ) {
        await handleSubmitStatus(notification._id, notification.type);
      }
      // For new notifications, you can add a PUT endpoint to mark as seen if desired
    });
    // Optionally, refresh notifications after marking as seen
    // setTimeout(() => {
    //   const email = cookies.get("email");
    //   if (email) fetchNotifications(email);
    // }, 1000);
  }, [userNotifications]);

  async function handleSubmitStatus(_id, type = "shooter") {
    try {
      // Determine which API endpoint to use based on notification type
      const endpoint =
        type === "comment"
          ? "/api/comment-notification"
          : type === "match_request" || type === "hickie"
          ? "/api/notifications"
          : "/api/shooter";

      await fetch(endpoint, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          _id: _id,
          seen: true,
        }),
      });
    } catch (error) {
      console.error("Error updating notification status:", error);
    }
  }

  function extractTimeFromTimestamp(timestamp) {
    const dateObject = new Date(timestamp);
    const hours = dateObject.getHours();
    const minutes = dateObject.getMinutes();
    return `${hours}:${minutes < 10 ? "0" : ""}${minutes}`;
  }

  if (!userNotifications.length) return <div>No notifications</div>;

  return (
    <div className="lg:pt-24 pt-6 space-y-12 relative w-full items-center">
      <div className="space-y-2 px-6">
        <p className="font-bold text-3xl">Notifications</p>
        <p className="text-gray-700">
          You have {unseenNotifications.length} new notifications
        </p>
      </div>
      <div>
        {userNotifications.map((post) => {
          // New notification type (from Notifications collection)
          if (
            post.type === "match_request" ||
            post.type === "hickie" ||
            post.type === "new_type"
          ) {
            return (
              <a key={post._id} href={post.link}>
                <NotificationItem
                  profilePicture={post.senderData?.profilePicture}
                  name={post.senderData?.name}
                  surname={post.senderData?.surname}
                  time={extractTimeFromTimestamp(
                    post.timestamp || post.createdAt
                  )}
                  message={post.message}
                  notificationType={post.type}
                  notificationId={post._id}
                />
              </a>
            );
          }
          // Old comment notification
          if (post.type === "comment") {
            return (
              <NotificationItem
                key={post._id}
                profilePicture={post.senderProfilePicture}
                name={post.senderName}
                surname={post.senderSurname}
                time={extractTimeFromTimestamp(post.timestamp)}
                message={`commented on your post: "${post.commentText?.substring(
                  0,
                  30
                )}${post.commentText?.length > 30 ? "..." : ""}"`}
                postImage={post.postImage}
                notificationType="comment"
                postId={post.postId}
                notificationId={post._id}
              />
            );
          }
          // Old shooter notification
          return (
            <NotificationItem
              key={post._id}
              profilePicture={post.senderData?.profilePicture}
              name={post.name}
              surname={post.surname}
              time={extractTimeFromTimestamp(post.timestamp)}
              senderData={post.senderData}
              shooter={post.shooter}
              notificationType="shooter"
              notificationId={post._id}
            />
          );
        })}
      </div>
    </div>
  );
}
