const { default: clientPromise } = require("@/lib/mongodb");

export async function GET(req) {
  try {
    const { searchParams } = new URL(req.url);
    const targetEmail = searchParams.get("targetEmail");
    if (!targetEmail) {
      return new Response(JSON.stringify({ error: "Missing targetEmail" }), { status: 400 });
    }
    const client = await clientPromise;
    const db = client.db("Pumpkin");
    
    // Try finding the user in "Users" first, then fall back to "users"
    let user = await db.collection("Users").findOne(
      { email: targetEmail },
      { projection: { receivedPumps: 1, pumpedBy: 1, _id: 0 } }
    );

    if (!user) {
      user = await db.collection("users").findOne(
        { email: targetEmail },
        { projection: { receivedPumps: 1, pumpedBy: 1, _id: 0 } }
      );
    }

    if (!user) {
      return new Response(JSON.stringify({ error: "User not found" }), { status: 404 });
    }

    return new Response(
      JSON.stringify({
        success: true,
        receivedPumps: user.receivedPumps || 0,
        pumpedBy: user.pumpedBy || [],
      }),
      { status: 200 }
    );
  } catch (error) {
    console.error("Error fetching pump data:", error);
    return new Response(JSON.stringify({ error: "Internal server error" }), { status: 500 });
  }
}

export async function PUT(req) {
  console.log("--- New PUT request to /api/profile-pump ---");
  try {
    const body = await req.json();
    console.log("Request Body:", body);

    const { targetEmail, pumpedBy, receivedPumps } = body;
    console.log("Extracted Data:", { targetEmail, pumpedBy, receivedPumps });

    if (!targetEmail) {
      console.log("Error: Missing targetEmail");
      return new Response(JSON.stringify({ error: "Missing targetEmail" }), { status: 400 });
    }

    const client = await clientPromise;
    const db = client.db("Pumpkin");
    console.log("Database connected.");

    // First, try to update in the "Users" collection
    let updateResult = await db.collection("Users").updateOne(
      { email: targetEmail },
      { $set: { pumpedBy, receivedPumps } }
    );

    // If no user was matched, try updating in the "users" collection
    if (updateResult.matchedCount === 0) {
      console.log("User not found in 'Users' collection, trying 'users' collection.");
      updateResult = await db.collection("users").updateOne(
        { email: targetEmail },
        { $set: { pumpedBy, receivedPumps } }
      );
    }

    console.log("MongoDB updateOne Result:", updateResult);

    if (updateResult.matchedCount === 0) {
      console.log("Update failed: User not found with email in either collection:", targetEmail);
      return new Response(JSON.stringify({ success: false, error: "User not found" }), { status: 404 });
    }

    if (updateResult.modifiedCount === 0) {
      console.log("Update completed but no changes were made (data was already up-to-date).");
    } else {
      console.log("Update successful: User pump data updated.");
    }

    return new Response(
      JSON.stringify({
        success: true,
        message: "Pump data updated successfully",
        matchedCount: updateResult.matchedCount,
        modifiedCount: updateResult.modifiedCount,
      }),
      { status: 200 }
    );
  } catch (error) {
    console.error("Error updating pump data:", error);
    return new Response(JSON.stringify({ success: false, error: "Internal server error" }), { status: 500 });
  }
}
