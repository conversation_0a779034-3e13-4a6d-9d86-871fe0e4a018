const { NextResponse } = require('next/server');
const { default: clientPromise } = require('@/lib/mongodb');

async function getDb() {
  const client = await clientPromise;
  return client.db('Pumpkin');
}

export async function GET(req) {
  const db = await getDb();
  const { searchParams } = new URL(req.url);
  const receiverEmail = searchParams.get('receiverEmail');
  if (!receiverEmail) {
    return NextResponse.json({ message: 'Missing receiverEmail' }, { status: 400 });
  }
  try {
    const notifications = await db
      .collection('Notifications')
      .find({ receiverEmail })
      .sort({ createdAt: -1 })
      .toArray();
    return NextResponse.json(notifications);
  } catch (error) {
    return NextResponse.json({ message: 'Error fetching notifications', error: error.message }, { status: 500 });
  }
}