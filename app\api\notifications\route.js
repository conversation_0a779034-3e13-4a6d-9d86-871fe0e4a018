const { NextResponse } = require("next/server");
const { MongoClient, ObjectId } = require("mongodb");
const { default: clientPromise } = require("@/lib/mongodb");

async function getDb() {
  const client = await clientPromise;
  return client.db("Pumpkin");
}

export async function GET(req) {
  const db = await getDb();
  const { searchParams } = new URL(req.url);
  const receiverEmail = searchParams.get("receiverEmail");
  if (!receiverEmail) {
    return NextResponse.json(
      { message: "Missing receiverEmail" },
      { status: 400 }
    );
  }
  try {
    const notifications = await db
      .collection("Notifications")
      .find({ receiverEmail })
      .sort({ createdAt: -1 })
      .toArray();
    return NextResponse.json(notifications);
  } catch (error) {
    return NextResponse.json(
      { message: "Error fetching notifications", error: error.message },
      { status: 500 }
    );
  }
}

// Mark notification as seen
export async function PUT(req) {
  const db = await getDb();
  const body = await req.json();
  const { _id, seen } = body;

  try {
    // Convert the string _id to ObjectId
    const objectId = new ObjectId(_id);

    // Update the notification
    const updatedData = await db
      .collection("Notifications")
      .updateOne({ _id: objectId }, { $set: { seen } });

    return NextResponse.json({ success: true, updated: updatedData });
  } catch (error) {
    console.error("Error updating notification:", error);
    return NextResponse.json({ error: error.message }, { status: 500 });
  }
}
