@font-face {
  font-family: 'Outfit';
  src: url('/fonts/Outfit.woff2') format('woff2'),
       url('/fonts/Outfit.woff') format('woff');
  font-weight: normal;
  font-style: normal;
  font-display: swap;
}
@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom gradient classes for buttons */
.gradient-darkblue-black {
  background: linear-gradient(to right, #000000, #2D3A4B); /* Dark blue-ish and black gradient */
  /* You might want to add hover effects here if desired */
  transition: background 0.3s ease;
}

.gradient-red-orange {
  background: linear-gradient(to right, #FF4500, #FF8C00); /* Reddish and orange-ish gradient */
  /* You might want to add hover effects here if desired */
  transition: background 0.3s ease;
}

/* Allow card shadows to show outside the carousel */
.react-multi-carousel-list {
  overflow: visible !important;
}

.font-Outfit {
  font-family: 'Outfit', sans-serif;
}

.react-multi-carousel-arrow {
  z-index: -999 !important;
}
/* --- NEW: Styles specifically for the active card zoom effect --- */

/* Apply a smooth transition to all carousel items */
.matches-carousel-container .react-multi-carousel-item {
  transition: transform 0.3s ease-in-out, opacity 0.3s ease-in-out; /* Smooth transition for zoom and opacity */
}

/* Style for the active (currently displayed) carousel item */
.matches-carousel-container .react-multi-carousel-item--active {
  transform: scale(1.10); /* Scales the active card by 3% */
  z-index: 10; /* Ensures the active card is visually on top of inactive ones */
  opacity: 1; /* Ensure active item is fully opaque */
}

/* Optional: Slightly dim inactive items for better focus on the active one */
.matches-carousel-container .react-multi-carousel-item:not(.react-multi-carousel-item--active) {
  opacity: 0.8; /* Slightly dim inactive items */
  /* You can uncomment the line below if you want inactive items to also scale down slightly: */
  /* transform: scale(0.98); */
}

/* --- IMPORTANT NOTE --- */
/* The following CSS rules (from previous steps) are crucial for ensuring */
/* only one card is displayed at a time and preventing partial cards. */
/* While not strictly "zoom-only," they are vital for the zoom to look correct. */
/* If you still see multiple/partial cards after these changes, you will likely need */
/* to re-add the `max-w` definitions in `tailwind.config.js` and apply */
/* the `max-w-card-*` classes to the carousel's parent div and the card itself. */
/* You might also need the `flex: 0 0 100%` rules if `items: 1` alone isn't enough. */

/* These rules help force single item display and remove unwanted spacing */
.matches-carousel-container .react-multi-carousel-track {
  display: flex;
  width: 100% !important;
}

.matches-carousel-container .react-multi-carousel-item {
  flex: 0 0 100% !important;
  max-width: 100% !important;
  overflow: hidden !important; /* Crucial to prevent partial visibility */
  margin-left: 0 !important; /* Remove any default margins between items */
}