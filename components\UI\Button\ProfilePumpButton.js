"use client";

import React, { useEffect, useState } from "react";
import { useCookies } from "next-client-cookies";
import { BiPlus } from "react-icons/bi";
import { RxCross1 } from "react-icons/rx";
import CircleIconButton from "./CircleIconButton";

export default function ProfilePumpButton({ targetEmail, initialReceivedPumps, initialPumpedBy, onPumpUpdate }) {
  const cookies = useCookies();
  const currentUserEmail = cookies.get("email");

  const [pumped, setPumped] = useState(false);
  const [pumpCount, setPumpCount] = useState(initialReceivedPumps || 0);
  const [pumpedBy, setPumpedBy] = useState(initialPumpedBy || []);

  // Sync internal state with props when initialReceivedPumps/initialPumpedBy or targetEmail changes
  useEffect(() => {
    setPumpCount(initialReceivedPumps || 0);
    setPumpedBy(initialPumpedBy || []);
    setPumped((initialPumpedBy || []).includes(currentUserEmail));
  }, [initialReceivedPumps, initialPumpedBy, currentUserEmail, targetEmail]);

  const togglePump = async () => {
    if (!currentUserEmail) {
        alert("Please log in to send a Pump.");
        return;
    }

    const isCurrentlyPumped = pumped; // Store current state before optimistic update

    // Calculate new state values
    let newPumpedBy;
    let newPumpCount;

    if (isCurrentlyPumped) {
      // User is un-pumping
      newPumpedBy = pumpedBy.filter(email => email !== currentUserEmail);
      newPumpCount = pumpCount - 1;
    } else {
      // User is pumping
      newPumpedBy = [...pumpedBy, currentUserEmail];
      newPumpCount = pumpCount + 1;
    }

    // Optimistically update local state (UI responds immediately)
    setPumped(!isCurrentlyPumped);
    setPumpedBy(newPumpedBy);
    setPumpCount(newPumpCount);

    try {
      const res = await fetch("/api/profile-pump", {
        method: "PUT",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          targetEmail,
          pumpedBy: newPumpedBy,
          receivedPumps: newPumpCount,
        }),
      });
    
       const result = await res.json();

      if (!res.ok || !result.success) {
        // If API call failed, revert local state
        console.error("ProfilePumpButton: API request failed. Reverting optimistic UI. Status:");
        setPumped(isCurrentlyPumped);
        setPumpedBy(pumpedBy); // Revert to original pumpedBy
        setPumpCount(pumpCount); // Revert to original pumpCount
        alert("Failed to update Pump. Please try again."); // Provide user feedback
      } else {
        // If API call succeeded, confirm the update to parent (MatchesHome)
        // Parent will update its 'users' array, which will then flow back as new props
        // to this component, solidifying the change.
        if (onPumpUpdate) {
            onPumpUpdate(targetEmail, newPumpCount, newPumpedBy);
        }
      }
    } catch (error) {
      console.error("ProfilePumpButton: Network error during API request:", error);
      // Revert optimistic UI on network error
      setPumped(isCurrentlyPumped);
      setPumpedBy(pumpedBy);
      setPumpCount(pumpCount);
      alert("Network error. Please check your connection and try again.");
    }
  };

  return (
    <CircleIconButton
      onClick={togglePump}
      icon={BiPlus}
      isPumped={pumped}
    />
  );
}
